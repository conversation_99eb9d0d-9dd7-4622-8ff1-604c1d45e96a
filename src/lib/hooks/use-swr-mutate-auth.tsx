'use client';

import useSWRMutation from 'swr/mutation';
import {
  authDelete,
  authGet,
  authPatch,
  authPost,
  authPut,
} from '@/lib/utils/auth-fetch';

type Method = 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'GET';

export function useSWRMutateWithAuth(urlToUse: string, method?: Method, asJsonResponse = true) {
  const { data, isMutating, error, trigger } = useSWRMutation<
    unknown,
    Error,
    string,
    unknown
  >(urlToUse, (url, { arg }: { arg: unknown }) => {
    switch (method) {
      case 'POST':
        return authPost(url, arg, asJsonResponse);

      case 'PUT':
        return authPut(url, arg);

      case 'GET':
        return authGet(url, arg as Record<string, string | number>);

      case 'PATCH':
        return authPatch(url, arg);

      case 'DELETE':
        return authDelete(url, arg);

      default:
        return authPost(url, arg, asJsonResponse);
    }
  });

  return { data, isMutating, error, trigger };
}
