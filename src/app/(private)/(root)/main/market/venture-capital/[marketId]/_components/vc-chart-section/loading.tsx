import { Card, CardContent } from '@/components/card';
import { Skeleton } from '@/components';
import { ChartNoAxesCombinedIcon } from 'lucide-react';

export default function VCChartSectionLoading() {
  return (
    <div className="charts-section h-full">
      <div className="sand-chard h-full">
        <Card className="shadow-none rounded-md h-full">
          <CardContent className="pt-6 flex flex-col h-full">
            <div className="chart-title">
              <h2 className="flex gap-x-2 text-lg items-center">
                <ChartNoAxesCombinedIcon size={18} />
                <span style={{ fontFamily: 'var(--onest)' }}>Market Chart</span>
              </h2>
            </div>

            <Skeleton className="w-full flex-1 mt-4" />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
