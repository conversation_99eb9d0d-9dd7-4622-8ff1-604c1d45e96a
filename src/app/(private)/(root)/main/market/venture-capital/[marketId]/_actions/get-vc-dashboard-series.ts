'use server';

import { serverAuthGet } from '@/lib/utils/server-auth-fetch';

export async function getVcDashboardSeries(marketId: string, filter: string | undefined) {
  'use server';

  let finalFilter = '';

  if ((filter !== 'CONTRACTS' && filter !== 'COMPANIES' && filter !== 'CUSTOMERS') || filter === undefined) {
    finalFilter = 'COMPANIES';
  } else {
    finalFilter = filter;
  }

  return serverAuthGet(`/venture-capital/market/${marketId}/series`, {
    series: finalFilter
  });
}
