'use server';

import { server<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/utils/server-auth-fetch';
import { config } from '@/lib/config';
import { VentureCapitalMarket } from '@/app/(private)/(root)/main/market/_lib/types/market-segment.type';

export async function getVentureCapitalDashboardDetails(
  marketId: string,
): Promise<VentureCapitalMarket | null> {
  'use server';

  const baseUrl = config.api.base_url_for_server_side;

  if (!baseUrl) {
    throw new Error('Server-side API base URL not configured');
  }

  const url = `${baseUrl}/market/VENTURE_CAPITAL/${marketId}`;

  return serverAuthFetch<PERSON><PERSON><VentureCapitalMarket>(url, {
    cache: 'force-cache'
  });
}
