'use client';

import { Sand<PERSON><PERSON> } from '@/components/charts/sand-chart';
import { Card, CardContent } from '@/components/card';
import { SeriesOption } from 'echarts';
import { useSwrAuth } from '@/lib/hooks/use-swr-auth';
import { config } from '@/lib/config';
import { CHART_COLORS } from '@/lib/styles/chart-colors';
import { shuffle } from 'lodash';
import { hexToRgba } from '@/lib/styles/hex-to-rgba';
import { ChartNoAxesCombinedIcon } from 'lucide-react';
import { cn } from '@/lib/styles/utils';

type VcChartData = {
  name: string;
  years: Record<string, number>;
  totalFunding: number;
};

export function VCChartSection({
  marketId,
  filter = 'COMPANIES',
  height,
  className,
}: {
  marketId: string;
  filter?: string;
  height?: string;
  className?: string;
}) {
  const apiUrl = config.api.base_url;
  const url = `${apiUrl}/venture-capital/market/${marketId}/chart?series=${filter}`;

  const { data } = useSwrAuth<VcChartData[]>(url);

  const legends =
    !!data && data.length > 0 && data[0]?.years
      ? Object.keys(data[0].years)
      : ['2020', '2021', '2022', '2023', '2024', '2025', '2026', '2027']; // Placeholder

  return (
    <div className={cn('charts-section', className)}>
      <div className="sand-chard h-full">
        <Card className="shadow-none rounded-md h-full">
          <CardContent className="pt-6 flex flex-col h-full">
            <div className="chart-title">
              <h2 className="flex gap-x-2 text-lg items-center">
                <ChartNoAxesCombinedIcon size={18} />

                <span style={{ fontFamily: 'var(--onest)' }}>Market Chart</span>
              </h2>
            </div>

            <SandChart
              height={height ?? '652px'}
              className="w-full"
              series={createGradientSeries(data ?? [], CHART_COLORS)}
              programLegends={legends}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function createGradientSeries(dataList: VcChartData[], colorList: string[]) {
  const shuffledArray = shuffle(colorList).slice(0, dataList.length);

  return dataList
    .filter((item) => item['years'])
    .map((data, index) => {
      const baseColor = shuffledArray[index];

      return {
        name: data['name'],
        type: 'line',
        stack: 'Total',
        smooth: true,
        showSymbol: false,
        lineStyle: {
          color: baseColor,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: hexToRgba(baseColor, 0.6) },
              { offset: 1, color: hexToRgba(baseColor, 0.1) },
            ],
          },
        },
        data: Object.values(data.years),
      };
    }) as SeriesOption[];
}
