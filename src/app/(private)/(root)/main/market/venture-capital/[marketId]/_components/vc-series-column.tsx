import { CAGRIndicator } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/cagr-indicator';
import { TamSamSomWidget } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/tam-sam-som-widget';
import { Separator } from '@/components/separator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/card';

type VCSeriesColumnProps = {
  contractTypes: Record<string, number>;
  seriesId: string;
  marketId: number;
  seriesName: string;
};

export function VCSeriesColumn({
  contractTypes,
  seriesName,
  seriesId,
  marketId,
}: VCSeriesColumnProps) {
  return (
    <div className="vc-series-column relative">
      <Card className="flex-1 shrink-0 mb-4">
        <CardHeader className="pb-0">
          <CardTitle className="flex items-center">
            <span>{seriesName}</span>
          </CardTitle>
        </CardHeader>

        <CardContent className="pt-2">
          <Separator className="mb-4" />

          <CAGRIndicator
            className="py-2.5"
            marketId={marketId}
            series={seriesId}
          />

          <TamSamSomWidget
            series={seriesId}
            marketId={marketId}
            className="mx-auto"
          />
        </CardContent>
      </Card>
    </div>
  );
}
