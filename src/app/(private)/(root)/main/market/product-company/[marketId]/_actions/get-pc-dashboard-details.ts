'use server';

import { server<PERSON>uth<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/utils/server-auth-fetch';
import { config } from '@/lib/config';
import { ProductCompanyMarket } from '@/app/(private)/(root)/main/market/_lib/types/market-segment.type';

export async function getPCDashboardDetails(
  marketId: string,
): Promise<ProductCompanyMarket | null> {
  'use server';

  const baseUrl = config.api.base_url_for_server_side;

  if (!baseUrl) {
    throw new Error('Server-side API base URL not configured');
  }

  const url = `${baseUrl}/market/PRODUCT_COMPANY/${marketId}`;

  return serverAuthFetch<PERSON>son<ProductCompanyMarket>(url, {
    cache: 'force-cache'
  });
}
