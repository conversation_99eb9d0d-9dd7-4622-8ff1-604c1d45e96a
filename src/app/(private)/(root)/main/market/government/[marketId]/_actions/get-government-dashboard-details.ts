'use server';

import { serverAuthGet } from '@/lib/utils/server-auth-fetch';
import { Contract } from './filter-contracts';

export async function getGovDashboardDetails(
  id: string,
  programsParam?: string | undefined,
  servicesParams?: string | undefined,
): Promise<{
  marketTitle: string;
  marketDescription: string | null;
  fundingDetails: Record<string, Record<string, number>>;
  contracts: Contract[];
}> {
  'use server';

  const params: Record<string, string> = {};

  if (programsParam) {
    params.peos = programsParam;
  }

  if (servicesParams) {
    params.services = servicesParams;
  }

  return serverAuthGet(`/market/GOVERNMENT/${id}`, params);
}

export async function getGovSeriesContracts(
  marketId: string,
  offices?: string[],
  series?: string[],
): Promise<{
  marketTitle: string;
  marketDescription: string | null;
  fundingDetails: Record<string, Record<string, number>>;
  contracts: Contract[];
}> {
  'use server';

  const params: Record<string, string> = {};

  if (offices && offices.length > 0) {
    params.offices = offices.join(',');
  }
  if (series && series.length > 0) {
    params.series = series.join(',');
  }

  return serverAuthGet(`/government/market/${marketId}/series/contracts`, params);
}
