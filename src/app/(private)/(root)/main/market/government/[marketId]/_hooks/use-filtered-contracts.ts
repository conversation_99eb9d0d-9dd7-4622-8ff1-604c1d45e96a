'use client';

import { config } from '@/lib/config';
import { useSWRMutateWithAuth } from '@/lib/hooks/use-swr-mutate-auth';
import { Contract } from '../_actions/filter-contracts';

// New contract type from the contracts list endpoint
export type ContractListItem = {
  id: string;
  awardId: string;
  contractType: string;
  series: string;
  officeName: string;
  recipient: string | null;
  amount: string;
  totalValue: string;
  description: string;
  startDate: string | null;
  endDate: string | null;
  naicsCode: string;
  naicsDescription: string | null;
  placeOfPerformance: string | null;
  state: string | null;
  country: string | null;
  productServiceCode: string | null;
  pscDescription: string;
  majorProgramCode: string;
};

export type FilteredContractsResponse = {
  contracts: Contract[];
};

// Response type for the new contracts list endpoint
export type ContractsListResponse = ContractListItem[];

export type ContractFilters = {
  marketId: string;
  offices?: string[];
  series?: string[];
  contractType?: string;
};

export function useFilteredContracts() {
  const configBaseUrl = config.api.base_url;

  // We'll use different hooks for different endpoints
  const oldEndpointKey = `${configBaseUrl}/government/market/contracts`;
  const { data: oldData, trigger: oldTrigger, isMutating: oldMutating, error: oldError } =
    useSWRMutateWithAuth(oldEndpointKey, 'GET');

  const getFilteredContracts = async (filters: ContractFilters) => {
    // Build query parameters object for authGet
    const queryParams: Record<string, string> = {};

    // Only add parameters if they have values
    if (filters.offices && filters.offices.length > 0) {
      queryParams.offices = filters.offices.join(',');
    }

    if (filters.series && filters.series.length > 0) {
      queryParams.series = filters.series.join(',');
    }

    if (filters.contractType) {
      queryParams.types = filters.contractType;
    }

    // Use the new contracts list endpoint if we have a marketId, as it provides better filtering
    const shouldUseNewEndpoint = !!filters.marketId;

    if (shouldUseNewEndpoint) {
      // Use the new contracts list endpoint that returns actual contract objects
      const { authFetchJson } = await import('@/lib/utils/auth-fetch');
      const url = `${configBaseUrl}/government/market/${filters.marketId}/contracts/list`;
      const queryString = new URLSearchParams(queryParams).toString();
      const fullUrl = queryString ? `${url}?${queryString}` : url;
      const contractsList = await authFetchJson<ContractsListResponse>(fullUrl);

      // Convert the new format to the expected format for backward compatibility
      return {
        contracts: contractsList.map((contract) => ({
          actionDate: contract.startDate || '',
          awardingOffice: contract.officeName,
          contractId: contract.awardId,
          description: contract.description,
          obligatedAmount: parseFloat(contract.amount) || 0,
          totalValue: parseFloat(contract.totalValue) || 0,
        }))
      };
    } else {
      // Use the old endpoint with the existing trigger
      return await oldTrigger(queryParams);
    }
  };

  return {
    data: oldData,
    getFilteredContracts,
    isMutating: oldMutating,
    error: oldError,
  };
}
