'use client';

import { config } from '@/lib/config';
import { useSWRMutateWithAuth } from '@/lib/hooks/use-swr-mutate-auth';
import { useSwrAuth } from '@/lib/hooks/use-swr-auth';
import { Contract } from '../_actions/filter-contracts';

// New contract type from the contracts list endpoint
export type ContractListItem = {
  id: string;
  awardId: string;
  contractType: string;
  series: string;
  officeName: string;
  recipient: string | null;
  amount: string;
  totalValue: string;
  description: string;
  startDate: string | null;
  endDate: string | null;
  naicsCode: string;
  naicsDescription: string | null;
  placeOfPerformance: string | null;
  state: string | null;
  country: string | null;
  productServiceCode: string | null;
  pscDescription: string;
  majorProgramCode: string;
};

export type FilteredContractsResponse = {
  contracts: Contract[];
};

// Response type for the new contracts list endpoint
export type ContractsListResponse = ContractListItem[];

export type ContractFilters = {
  marketId: string;
  offices?: string[];
  series?: string[];
  contractType?: string;
};

// Hook for both old and new contracts endpoints
export function useFilteredContracts() {
  const configBaseUrl = config.api.base_url;
  const { data, trigger, isMutating, error } = useSWRMutateWithAuth(
    `${configBaseUrl}/government/market/contracts`,
    'GET'
  );

  const getFilteredContracts = async (filters: ContractFilters) => {
    const queryParams: Record<string, string> = {};

    if (filters.offices && filters.offices.length > 0) {
      queryParams.offices = filters.offices.join(',');
    }

    if (filters.series && filters.series.length > 0) {
      queryParams.series = filters.series.join(',');
    }

    if (filters.contractType) {
      queryParams.types = filters.contractType;
    }

    // Use the new market-specific endpoint if marketId is provided
    if (filters.marketId) {
      const { authFetchJson } = await import('@/lib/utils/auth-fetch');
      const url = `${configBaseUrl}/government/market/${filters.marketId}/contracts/list`;
      const queryString = new URLSearchParams(queryParams).toString();
      const fullUrl = queryString ? `${url}?${queryString}` : url;
      const contractsList = await authFetchJson<ContractsListResponse>(fullUrl);

      // Convert the new format to the expected format for backward compatibility
      return {
        contracts: contractsList.map((contract: ContractListItem) => ({
          actionDate: contract.startDate || '',
          awardingOffice: contract.officeName,
          contractId: contract.awardId,
          description: contract.description,
          obligatedAmount: parseFloat(contract.amount) || 0,
          totalValue: parseFloat(contract.totalValue) || 0,
        }))
      };
    } else {
      // Use the old endpoint for backward compatibility
      return await trigger(queryParams);
    }
  };

  return {
    data,
    getFilteredContracts,
    isMutating,
    error,
  };
}

// Hook for the new market-specific contracts list endpoint
export function useMarketContractsList(
  marketId: string,
  offices?: string[],
  series?: string[],
  contractType?: string
) {
  const configBaseUrl = config.api.base_url;

  // Build query parameters
  const params = new URLSearchParams();
  if (offices && offices.length > 0) {
    params.set('offices', offices.join(','));
  }
  if (series && series.length > 0) {
    params.set('series', series.join(','));
  }
  if (contractType) {
    params.set('types', contractType);
  }

  const queryString = params.toString();
  const url = `${configBaseUrl}/government/market/${marketId}/contracts/list${queryString ? `?${queryString}` : ''}`;

  const { data, isLoading, error } = useSwrAuth<ContractsListResponse>(url);

  // Convert the new format to the expected format for backward compatibility
  const convertedData = data ? {
    contracts: data.map((contract: ContractListItem) => ({
      actionDate: contract.startDate || '',
      awardingOffice: contract.officeName,
      contractId: contract.awardId,
      description: contract.description,
      obligatedAmount: parseFloat(contract.amount) || 0,
      totalValue: parseFloat(contract.totalValue) || 0,
    }))
  } : undefined;

  return {
    data: convertedData,
    isLoading,
    error,
  };
}
