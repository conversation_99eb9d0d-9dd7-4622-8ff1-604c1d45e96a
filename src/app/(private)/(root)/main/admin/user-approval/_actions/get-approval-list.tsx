'use server';

import { checkAdminAccess } from '@/lib/utils/admin-guard';
import { serverAuthGet } from '@/lib/utils/server-auth-fetch';
import { UserApproval } from '@/app/(private)/(root)/main/admin/user-approval/_lib/types/user-approval';

export async function getApprovalList(): Promise<UserApproval[]> {
  'use server';

  await checkAdminAccess();

  return serverAuthGet<UserApproval[]>('/user');
}
