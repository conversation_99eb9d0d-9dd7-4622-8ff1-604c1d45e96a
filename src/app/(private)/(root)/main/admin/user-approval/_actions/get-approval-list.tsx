'use server';

import { authOptions } from '@/lib/config/nextauth.config';
import { getServerSession } from 'next-auth';
import { config } from '@/lib/config';
import { checkAdminAccess } from '@/lib/utils/admin-guard';
import { UserApproval } from '@/app/(private)/(root)/main/admin/user-approval/_lib/types/user-approval';

export async function getApprovalList(): Promise<UserApproval[]> {
  await checkAdminAccess();

  const session = await getServerSession(authOptions);

  if (!session) {
    throw new Error('No session found');
  }

  const configBaseUrl = config.api.base_url_for_server_side;
  const url = `${configBaseUrl}/user`;

  const request = await fetch(url, {
    headers: {
      Authorization: `Bearer ${session.accessToken}`,
    },
    method: 'GET',
  });

  return await request.json();
}
